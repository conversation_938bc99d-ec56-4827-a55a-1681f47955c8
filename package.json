{"name": "dizit-solution", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "create-admin": "node scripts/create-admin.js"}, "dependencies": {"@stripe/stripe-js": "^7.3.0", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "clsx": "^2.1.1", "dotenv": "^16.5.0", "framer-motion": "^12.15.0", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "mongodb": "^6.16.0", "next": "^15.1.0", "nodemailer": "^7.0.3", "p-retry": "^6.2.1", "pino": "^9.7.0", "postcss": "^8.5.3", "razorpay": "^2.9.6", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-datepicker": "^8.4.0", "react-dom": "^18.2.0", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-to-print": "^3.1.0", "stripe": "^18.1.0", "sweetalert2": "^11.22.0", "tailwind-merge": "^3.3.0", "twilio": "^5.6.1", "zod": "^3.24.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/lodash": "^4.17.17", "@types/lodash.debounce": "^4.0.9", "@types/node": "^22.15.21", "@types/nodemailer": "^6.4.17", "@types/p-retry": "^3.0.0", "@types/react": "^18.2.0", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^18.2.0", "@types/twilio": "^3.19.2", "autoprefixer": "^10.4.20", "eslint": "^9.0.0", "eslint-config-next": "15.1.0", "tailwindcss": "^3.4.15", "typescript": "^5.8.3"}}